import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Enhanced pricing validation schema
const pricingSchema = z.object({
  original_price: z.number().min(0, "السعر الأصلي يجب أن يكون أكبر من أو يساوي 0"),
  user_price: z.number().min(0, "سعر المستخدم يجب أن يكون أكبر من أو يساوي 0"),
  discount_price: z.number().min(0, "سعر الخصم يجب أن يكون أكبر من أو يساوي 0").optional(),
  distributor_price: z.number().min(0, "سعر الموزع يجب أن يكون أكبر من أو يساوي 0").optional()
}).refine((data) => data.user_price > data.original_price, {
  message: "سعر المستخدم يجب أن يكون أكبر من السعر الأصلي",
  path: ["user_price"]
}).refine((data) => !data.discount_price || data.discount_price < data.user_price, {
  message: "سعر الخصم يجب أن يكون أقل من سعر المستخدم",
  path: ["discount_price"]
}).refine((data) => !data.discount_price || data.discount_price > data.original_price, {
  message: "سعر الخصم يجب أن يكون أكبر من السعر الأصلي",
  path: ["discount_price"]
}).refine((data) => !data.discount_price || data.discount_price !== data.user_price, {
  message: "سعر الخصم لا يمكن أن يساوي سعر المستخدم",
  path: ["discount_price"]
}).refine((data) => !data.distributor_price || data.distributor_price < data.user_price, {
  message: "سعر الموزع يجب أن يكون أقل من سعر المستخدم",
  path: ["distributor_price"]
}).refine((data) => !data.distributor_price || data.distributor_price !== data.user_price, {
  message: "سعر الموزع لا يمكن أن يساوي سعر المستخدم",
  path: ["distributor_price"]
})

// Validation schemas
const productCreateSchema = z.object({
  title: z.string().min(1, "عنوان المنتج مطلوب").max(200, "عنوان المنتج لا يمكن أن يزيد عن 200 حرف"),
  description: z.string().min(10, "الوصف يجب أن يكون على الأقل 10 أحرف").max(2000, "الوصف لا يمكن أن يزيد عن 2000 حرف"),
  cover_image: z.string().url("رابط صورة الغلاف غير صالح"),
  category: z.string().min(1, "فئة المنتج مطلوبة"),
  category_id: z.string().uuid("معرف الفئة غير صالح").optional(),
  tags: z.array(z.string()).default([]),
  featured: z.boolean().default(false),
  // Product-level pricing (used when no packages exist)
  original_price: z.number().min(0).optional(),
  user_price: z.number().min(0).optional(),
  discount_price: z.number().min(0).optional(),
  distributor_price: z.number().min(0).optional(),
  packages: z.array(z.object({
    name: z.string().min(1, "اسم الحزمة مطلوب"),
    // Legacy price field for backward compatibility
    price: z.number().min(0, "السعر يجب أن يكون أكبر من أو يساوي 0").optional(),
    // Enhanced pricing fields
    original_price: z.number().min(0, "السعر الأصلي مطلوب"),
    user_price: z.number().min(0, "سعر المستخدم مطلوب"),
    discount_price: z.number().min(0).optional(),
    distributor_price: z.number().min(0).optional(),
    image: z.string().url().optional(),
    description: z.string().optional(),
    use_product_image: z.boolean().default(false),
    image_reference_type: z.enum(['url', 'product_image']).default('url'),
    has_digital_codes: z.boolean().default(false)
  }).refine((data) => data.user_price > data.original_price, {
    message: "سعر المستخدم يجب أن يكون أكبر من السعر الأصلي",
    path: ["user_price"]
  }).refine((data) => !data.discount_price || data.discount_price < data.user_price, {
    message: "سعر الخصم يجب أن يكون أقل من سعر المستخدم",
    path: ["discount_price"]
  }).refine((data) => !data.discount_price || data.discount_price > data.original_price, {
    message: "سعر الخصم يجب أن يكون أكبر من السعر الأصلي",
    path: ["discount_price"]
  }).refine((data) => !data.discount_price || data.discount_price !== data.user_price, {
    message: "سعر الخصم لا يمكن أن يساوي سعر المستخدم",
    path: ["discount_price"]
  }).refine((data) => !data.distributor_price || data.distributor_price < data.user_price, {
    message: "سعر الموزع يجب أن يكون أقل من سعر المستخدم",
    path: ["distributor_price"]
  }).refine((data) => !data.distributor_price || data.distributor_price !== data.user_price, {
    message: "سعر الموزع لا يمكن أن يساوي سعر المستخدم",
    path: ["distributor_price"]
  })).optional()
}).refine((data) => {
  // If no packages, product-level pricing is required
  if (!data.packages || data.packages.length === 0) {
    return data.original_price !== undefined && data.user_price !== undefined && data.user_price > data.original_price
  }
  return true
}, {
  message: "يجب تحديد أسعار المنتج عند عدم وجود حزم",
  path: ["user_price"]
})

// Sanitization function
function sanitizeHtml(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

// GET /api/admin/products - Get products with pagination
export async function GET(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 50)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse pagination parameters
    const url = new URL(request.url)
    const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'))
    const limit = Math.min(50, Math.max(1, parseInt(url.searchParams.get('limit') || '10')))
    const offset = (page - 1) * limit
    const search = url.searchParams.get('search')
    const category = url.searchParams.get('category')

    // Get total count
    let countQuery = supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', profile.tenant_id)

    if (search) {
      countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }
    if (category) {
      countQuery = countQuery.eq('category', category)
    }

    const { count: totalProducts, error: countError } = await countQuery

    if (countError) {
      console.error('Products API - Count error:', countError)
      return NextResponse.json({ error: 'Failed to count products' }, { status: 500 })
    }

    // Get products with packages
    let productsQuery = supabase
      .from('products')
      .select(`
        id,
        slug,
        title,
        description,
        cover_image,
        category,
        category_id,
        tags,
        rating,
        comment_count,
        featured,
        original_price,
        user_price,
        discount_price,
        distributor_price,
        created_at,
        updated_at,
        packages (
          id,
          name,
          price,
          original_price,
          user_price,
          discount_price,
          distributor_price,
          image,
          description,
          use_product_image,
          image_reference_type,
          has_digital_codes
        ),
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('tenant_id', profile.tenant_id)

    if (search) {
      productsQuery = productsQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }
    if (category) {
      productsQuery = productsQuery.eq('category', category)
    }

    const { data: products, error } = await productsQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Products API - Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch products', details: error.message }, { status: 500 })
    }

    const totalPages = Math.ceil((totalProducts || 0) / limit)

    return NextResponse.json({
      success: true,
      products: products || [],
      pagination: {
        page,
        limit,
        total: totalProducts || 0,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error in GET /api/admin/products:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/admin/products - Create new product
export async function POST(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()

    // Sanitize input data
    const sanitizedData = {
      ...body,
      title: sanitizeHtml(body.title),
      description: sanitizeHtml(body.description),
      category: sanitizeHtml(body.category)
    }

    const validatedData = productCreateSchema.parse(sanitizedData)

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Check if slug already exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('slug', slug)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (existingProduct) {
      return NextResponse.json({ error: 'منتج بهذا الاسم موجود بالفعل' }, { status: 409 })
    }

    // Create product
    const { data: product, error: productError } = await supabase
      .from('products')
      .insert({
        tenant_id: profile.tenant_id,
        slug,
        title: validatedData.title,
        description: validatedData.description,
        cover_image: validatedData.cover_image,
        category: validatedData.category,
        category_id: validatedData.category_id,
        tags: validatedData.tags,
        featured: validatedData.featured,
        // Product-level pricing (when no packages exist)
        original_price: validatedData.original_price,
        user_price: validatedData.user_price,
        discount_price: validatedData.discount_price,
        distributor_price: validatedData.distributor_price
      })
      .select()
      .single()

    if (productError) {
      console.error('Product creation error:', productError)
      return NextResponse.json({ error: 'Failed to create product', details: productError.message }, { status: 500 })
    }

    // Create packages (if any)
    if (validatedData.packages && validatedData.packages.length > 0) {
      const packagesData = validatedData.packages.map(pkg => ({
        tenant_id: profile.tenant_id,
        product_id: product.id,
        name: sanitizeHtml(pkg.name),
        // Keep legacy price field for backward compatibility
        price: pkg.user_price || pkg.price || 0,
        // Enhanced pricing fields
        original_price: pkg.original_price,
        user_price: pkg.user_price,
        discount_price: pkg.discount_price,
        distributor_price: pkg.distributor_price,
        image: pkg.image || '',
        description: pkg.description ? sanitizeHtml(pkg.description) : null,
        use_product_image: pkg.use_product_image,
        image_reference_type: pkg.image_reference_type,
        has_digital_codes: pkg.has_digital_codes
      }))

      const { error: packagesError } = await supabase
        .from('packages')
        .insert(packagesData)

      if (packagesError) {
        // Rollback product creation
        await supabase.from('products').delete().eq('id', product.id)
        console.error('Packages creation error:', packagesError)
        return NextResponse.json({ error: 'Failed to create packages', details: packagesError.message }, { status: 500 })
      }
    }



    return NextResponse.json({
      success: true,
      product,
      message: 'تم إنشاء المنتج بنجاح'
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/admin/products:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'بيانات غير صالحة',
        details: error.errors.map(e => e.message).join(', ')
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
