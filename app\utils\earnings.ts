import type { Order, Product, Package } from '../types'

/**
 * Earnings calculation utilities for the dashboard
 * Calculates profit margins based on cost vs selling price
 */

export interface EarningsData {
  totalRevenue: number      // Total sales amount
  totalCost: number         // Total cost of goods sold
  totalProfit: number       // Total profit (revenue - cost)
  profitMargin: number      // Profit margin percentage
  orderCount: number        // Number of orders
  averageOrderValue: number // Average order value
}

export interface OrderEarnings {
  orderId: string
  revenue: number           // Amount paid by customer
  cost: number             // Cost of the package/product
  profit: number           // Profit from this order
  profitMargin: number     // Profit margin percentage
}

/**
 * Calculate earnings for a single order - FIXED VERSION
 */
export function calculateOrderEarnings(
  order: Order,
  products: Product[],
  packages: Package[]
): OrderEarnings {
  const product = products.find(p => p.id === order.product_id)
  const packageItem = packages.find(p => p.id === order.package_id)

  // Revenue is already in USD (converted at order creation)
  const revenue = order.amount
  let cost = 0

  // Get quantity from custom_data (important for multi-quantity orders)
  const quantity = order.custom_data?.quantity || 1

  if (packageItem && packageItem.original_price) {
    // Use package cost (this is the wholesale/cost price)
    cost = packageItem.original_price * quantity
  } else if (product && product.original_price) {
    // Fallback to product-level cost
    cost = product.original_price * quantity
  } else {
    // Debug: Log when cost data is missing
    console.warn(`Missing cost data for order ${order.id}, package: ${order.package_id}`)
    cost = 0
  }

  const profit = revenue - cost
  const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0

  return {
    orderId: order.id,
    revenue,
    cost,
    profit,
    profitMargin
  }
}

/**
 * Calculate total earnings from multiple orders
 */
export function calculateTotalEarnings(
  orders: Order[], 
  products: Product[], 
  packages: Package[]
): EarningsData {
  const completedOrders = orders.filter(order => order.status === 'completed')
  
  let totalRevenue = 0
  let totalCost = 0
  
  completedOrders.forEach(order => {
    const earnings = calculateOrderEarnings(order, products, packages)
    totalRevenue += earnings.revenue
    totalCost += earnings.cost
  })
  
  const totalProfit = totalRevenue - totalCost
  const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0
  const averageOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0
  
  return {
    totalRevenue,
    totalCost,
    totalProfit,
    profitMargin,
    orderCount: completedOrders.length,
    averageOrderValue
  }
}

/**
 * Calculate earnings by product
 */
export function calculateEarningsByProduct(
  orders: Order[], 
  products: Product[], 
  packages: Package[]
): Array<{
  productId: string
  productName: string
  orderCount: number
  totalRevenue: number
  totalCost: number
  totalProfit: number
  profitMargin: number
}> {
  const productEarnings = new Map()
  
  const completedOrders = orders.filter(order => order.status === 'completed')
  
  completedOrders.forEach(order => {
    const product = products.find(p => p.id === order.product_id)
    if (!product) return
    
    const earnings = calculateOrderEarnings(order, products, packages)
    
    if (!productEarnings.has(product.id)) {
      productEarnings.set(product.id, {
        productId: product.id,
        productName: product.title,
        orderCount: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        profitMargin: 0
      })
    }
    
    const current = productEarnings.get(product.id)
    current.orderCount += 1
    current.totalRevenue += earnings.revenue
    current.totalCost += earnings.cost
    current.totalProfit += earnings.profit
  })
  
  // Calculate profit margins
  productEarnings.forEach(data => {
    data.profitMargin = data.totalRevenue > 0 ? (data.totalProfit / data.totalRevenue) * 100 : 0
  })
  
  return Array.from(productEarnings.values())
    .sort((a, b) => b.totalProfit - a.totalProfit) // Sort by highest profit
}

/**
 * Calculate earnings by time period
 */
export function calculateEarningsByPeriod(
  orders: Order[],
  products: Product[],
  packages: Package[],
  periodDays: number = 30,
  offsetDays: number = 0
): EarningsData {
  const endDate = new Date()
  endDate.setDate(endDate.getDate() - offsetDays)

  const startDate = new Date(endDate)
  startDate.setDate(startDate.getDate() - periodDays)

  const periodOrders = orders.filter(order => {
    const orderDate = new Date(order.created_at)
    return orderDate >= startDate && orderDate <= endDate && order.status === 'completed'
  })

  return calculateTotalEarnings(periodOrders, products, packages)
}

/**
 * Get top performing products by profit
 */
export function getTopProfitableProducts(
  orders: Order[], 
  products: Product[], 
  packages: Package[],
  limit: number = 5
) {
  const productEarnings = calculateEarningsByProduct(orders, products, packages)
  return productEarnings.slice(0, limit)
}

/**
 * Calculate profit margin for a specific package
 */
export function calculatePackageProfitMargin(pkg: Package): number {
  if (!pkg.user_price || !pkg.original_price || pkg.user_price <= pkg.original_price) {
    return 0
  }
  
  return ((pkg.user_price - pkg.original_price) / pkg.user_price) * 100
}

/**
 * Calculate profit margin for a specific product
 */
export function calculateProductProfitMargin(product: Product): number {
  if (!product.user_price || !product.original_price || product.user_price <= product.original_price) {
    return 0
  }
  
  return ((product.user_price - product.original_price) / product.user_price) * 100
}

/**
 * Get earnings breakdown by customer currency
 */
export function getEarningsByCurrency(
  orders: Order[],
  products: Product[],
  packages: Package[]
): Array<{
  currency: string
  orderCount: number
  totalRevenueUSD: number
  totalCostUSD: number
  totalProfitUSD: number
  profitMarginPercent: number
  originalCurrencyAmount: number
}> {
  const completedOrders = orders.filter(order => order.status === 'completed')
  const currencyMap = new Map()

  completedOrders.forEach(order => {
    const earnings = calculateOrderEarnings(order, products, packages)
    const currency = order.custom_data?.currency_code || 'USD'
    const originalAmount = order.custom_data?.amount_in_currency || order.amount

    if (!currencyMap.has(currency)) {
      currencyMap.set(currency, {
        currency,
        orderCount: 0,
        totalRevenueUSD: 0,
        totalCostUSD: 0,
        totalProfitUSD: 0,
        profitMarginPercent: 0,
        originalCurrencyAmount: 0
      })
    }

    const current = currencyMap.get(currency)
    current.orderCount += 1
    current.totalRevenueUSD += earnings.revenue
    current.totalCostUSD += earnings.cost
    current.totalProfitUSD += earnings.profit
    current.originalCurrencyAmount += originalAmount
  })

  // Calculate profit margins for each currency
  currencyMap.forEach(data => {
    data.profitMarginPercent = data.totalRevenueUSD > 0
      ? (data.totalProfitUSD / data.totalRevenueUSD) * 100
      : 0
  })

  return Array.from(currencyMap.values())
    .sort((a, b) => b.totalProfitUSD - a.totalProfitUSD)
}

/**
 * Format earnings data for display
 */
export function formatEarningsData(earnings: EarningsData) {
  return {
    totalRevenue: earnings.totalRevenue.toFixed(2),
    totalCost: earnings.totalCost.toFixed(2),
    totalProfit: earnings.totalProfit.toFixed(2),
    profitMargin: earnings.profitMargin.toFixed(1) + '%',
    orderCount: earnings.orderCount.toString(),
    averageOrderValue: earnings.averageOrderValue.toFixed(2)
  }
}
