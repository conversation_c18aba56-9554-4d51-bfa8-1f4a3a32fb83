/**
 * Optimized database queries with caching and performance monitoring
 */

import { createClient } from './supabase/server'
import cacheManager, { cacheUtils } from './cache'
import { performanceThresholds } from './config'

interface QueryOptions {
  useCache?: boolean
  cacheTTL?: number
  tags?: string[]
}

interface PaginationOptions {
  page?: number
  limit?: number
  offset?: number
}

interface ProductFilters {
  search?: string
  category?: string
  featured?: boolean
  tenantId: string
}

interface OrderFilters {
  status?: string
  userId?: string
  dateFrom?: string
  dateTo?: string
  tenantId: string
}

/**
 * Performance monitoring wrapper
 */
function withPerformanceMonitoring<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  operationName: string
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now()
    
    try {
      const result = await fn(...args)
      const duration = Date.now() - startTime
      
      // Log slow queries
      if (duration > performanceThresholds.apiResponse) {
        console.warn(`Slow query detected: ${operationName} took ${duration}ms`)
      }
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`Query failed: ${operationName} after ${duration}ms`, error)
      throw error
    }
  }
}

/**
 * Optimized products queries
 */
export const productQueries = {
  /**
   * Get products with optimized pagination and caching
   */
  getProducts: withPerformanceMonitoring(async (
    filters: ProductFilters,
    pagination: PaginationOptions = {},
    options: QueryOptions = {}
  ) => {
    const { page = 1, limit = 10 } = pagination
    const { useCache = true, cacheTTL, tags = ['products'] } = options
    
    const cacheKey = cacheUtils.productKey(filters.tenantId, page, {
      search: filters.search,
      category: filters.category,
      featured: filters.featured
    })
    
    if (useCache) {
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached
    }
    
    const supabase = await createClient()
    const offset = (page - 1) * limit
    
    // Build optimized query with selective fields
    let query = supabase
      .from('products')
      .select(`
        id,
        slug,
        title,
        description,
        cover_image,
        category,
        category_id,
        tags,
        rating,
        comment_count,
        featured,
        original_price,
        user_price,
        discount_price,
        distributor_price,
        created_at,
        updated_at,
        packages!inner (
          id,
          name,
          original_price,
          user_price,
          discount_price,
          distributor_price,
          image,
          description,
          use_product_image,
          image_reference_type,
          has_digital_codes
        ),
        categories (
          id,
          name,
          slug
        )
      `, { count: 'exact' })
      .eq('tenant_id', filters.tenantId)
    
    // Apply filters
    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }
    if (filters.category) {
      query = query.eq('category', filters.category)
    }
    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured)
    }
    
    // Apply pagination and ordering
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) throw error
    
    const result = {
      products: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        hasNext: page < Math.ceil((count || 0) / limit),
        hasPrev: page > 1
      }
    }
    
    // Cache the result
    if (useCache) {
      const ttl = cacheTTL || cacheUtils.getTTL('products')
      cacheManager.set(cacheKey, result, ttl, [...tags, `tenant:${filters.tenantId}`])
    }
    
    return result
  }, 'getProducts'),

  /**
   * Get single product with packages
   */
  getProduct: withPerformanceMonitoring(async (
    slug: string,
    tenantId: string,
    options: QueryOptions = {}
  ) => {
    const { useCache = true, cacheTTL, tags = ['products'] } = options
    const cacheKey = `product:${tenantId}:${slug}`
    
    if (useCache) {
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached
    }
    
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        packages (*),
        categories (*)
      `)
      .eq('tenant_id', tenantId)
      .eq('slug', slug)
      .single()
    
    if (error) throw error
    
    if (useCache && data) {
      const ttl = cacheTTL || cacheUtils.getTTL('products')
      cacheManager.set(cacheKey, data, ttl, [...tags, `tenant:${tenantId}`])
    }
    
    return data
  }, 'getProduct'),

  /**
   * Get featured products
   */
  getFeaturedProducts: withPerformanceMonitoring(async (
    tenantId: string,
    limit: number = 6,
    options: QueryOptions = {}
  ) => {
    return productQueries.getProducts(
      { tenantId, featured: true },
      { page: 1, limit },
      options
    )
  }, 'getFeaturedProducts')
}

/**
 * Optimized orders queries
 */
export const orderQueries = {
  /**
   * Get orders with optimized pagination and caching
   */
  getOrders: withPerformanceMonitoring(async (
    filters: OrderFilters,
    pagination: PaginationOptions = {},
    options: QueryOptions = {}
  ) => {
    const { page = 1, limit = 10 } = pagination
    const { useCache = true, cacheTTL, tags = ['orders'] } = options
    
    const cacheKey = cacheUtils.orderKey(filters.tenantId, filters.userId, page, {
      status: filters.status,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo
    })
    
    if (useCache) {
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached
    }
    
    const supabase = await createClient()
    const offset = (page - 1) * limit
    
    let query = supabase
      .from('orders')
      .select(`
        id,
        amount,
        status,
        custom_data,
        created_at,
        updated_at,
        worker_id,
        worker_action,
        worker_action_at,
        products (
          id,
          title,
          slug,
          cover_image
        ),
        packages (
          id,
          name,
          original_price,
          user_price,
          image
        ),
        user_profiles!orders_user_id_fkey (
          id,
          name,
          email
        ),
        worker_profiles:user_profiles!orders_worker_id_fkey (
          id,
          name,
          email,
          role
        )
      `, { count: 'exact' })
      .eq('tenant_id', filters.tenantId)
    
    // Apply filters
    if (filters.userId) {
      query = query.eq('user_id', filters.userId)
    }
    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.dateFrom) {
      query = query.gte('created_at', filters.dateFrom)
    }
    if (filters.dateTo) {
      query = query.lte('created_at', filters.dateTo)
    }
    
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) throw error
    
    const result = {
      orders: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        hasNext: page < Math.ceil((count || 0) / limit),
        hasPrev: page > 1
      }
    }
    
    // Cache the result (shorter TTL for orders)
    if (useCache) {
      const ttl = cacheTTL || cacheUtils.getTTL('orders')
      cacheManager.set(cacheKey, result, ttl, [...tags, `tenant:${filters.tenantId}`])
    }
    
    return result
  }, 'getOrders'),

  /**
   * Get earnings data with caching
   */
  getEarnings: withPerformanceMonitoring(async (
    tenantId: string,
    period: number = 30,
    options: QueryOptions = {}
  ) => {
    const { useCache = true, cacheTTL, tags = ['earnings'] } = options
    const cacheKey = cacheUtils.earningsKey(tenantId, period)
    
    if (useCache) {
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached
    }
    
    const supabase = await createClient()
    
    // Get completed orders with product and package data for earnings calculation
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        id,
        amount,
        product_id,
        package_id,
        created_at,
        products (
          id,
          title,
          original_price,
          user_price
        ),
        packages (
          id,
          name,
          original_price,
          user_price
        )
      `)
      .eq('tenant_id', tenantId)
      .eq('status', 'completed')
      .gte('created_at', new Date(Date.now() - period * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
    
    if (error) throw error
    
    // Cache the result
    if (useCache) {
      const ttl = cacheTTL || cacheUtils.getTTL('orders')
      cacheManager.set(cacheKey, orders || [], ttl, [...tags, `tenant:${tenantId}`])
    }
    
    return orders || []
  }, 'getEarnings')
}

/**
 * Cache invalidation helpers
 */
export const cacheInvalidation = {
  /**
   * Invalidate product-related cache when products change
   */
  onProductChange: (tenantId: string) => {
    cacheUtils.invalidateProducts(tenantId)
    cacheUtils.invalidateEarnings(tenantId) // Earnings depend on products
  },

  /**
   * Invalidate order-related cache when orders change
   */
  onOrderChange: (tenantId: string) => {
    cacheUtils.invalidateOrders(tenantId)
    cacheUtils.invalidateEarnings(tenantId)
  },

  /**
   * Invalidate all tenant-related cache
   */
  onTenantChange: (tenantId: string) => {
    cacheManager.invalidateByTag(`tenant:${tenantId}`)
  }
}
