#!/usr/bin/env node

/**
 * Database Migration Runner
 * Executes critical security fixes for the products management system
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  console.log('🚀 Starting critical security fixes migration...')
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '../docs/migrations/001_critical_security_fixes.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('📖 Migration file loaded successfully')
    
    // Execute the migration
    console.log('⚡ Executing migration...')
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })
    
    if (error) {
      console.error('❌ Migration failed:', error)
      process.exit(1)
    }
    
    console.log('✅ Migration executed successfully!')
    
    // Run verification queries
    console.log('🔍 Running verification checks...')
    
    // Check if tenant_id was added to packages
    const { data: packageColumns, error: colError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'packages')
      .eq('column_name', 'tenant_id')
    
    if (colError) {
      console.warn('⚠️ Could not verify tenant_id column:', colError.message)
    } else if (packageColumns && packageColumns.length > 0) {
      console.log('✅ tenant_id column added to packages table')
    } else {
      console.error('❌ tenant_id column not found in packages table')
    }
    
    // Check indexes
    const { data: indexes, error: idxError } = await supabase
      .from('pg_indexes')
      .select('indexname, tablename')
      .in('tablename', ['products', 'packages'])
      .like('indexname', 'idx_%tenant%')
    
    if (idxError) {
      console.warn('⚠️ Could not verify indexes:', idxError.message)
    } else {
      console.log(`✅ Created ${indexes?.length || 0} tenant-aware indexes`)
      indexes?.forEach(idx => {
        console.log(`   - ${idx.indexname} on ${idx.tablename}`)
      })
    }
    
    // Check RLS policies
    const { data: policies, error: polError } = await supabase
      .from('pg_policies')
      .select('policyname, tablename')
      .eq('tablename', 'packages')
    
    if (polError) {
      console.warn('⚠️ Could not verify RLS policies:', polError.message)
    } else {
      console.log(`✅ Updated RLS policies for packages table`)
      policies?.forEach(policy => {
        console.log(`   - ${policy.policyname}`)
      })
    }
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('📋 Next steps:')
    console.log('   1. Update API endpoints to use tenant_id in packages operations')
    console.log('   2. Fix duplicate field assignments in product creation')
    console.log('   3. Implement transaction safety for product+packages operations')
    console.log('   4. Update pricing validation logic')
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️ Migration interrupted by user')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️ Migration terminated')
  process.exit(1)
})

// Run the migration
runMigration().catch(error => {
  console.error('💥 Migration failed:', error)
  process.exit(1)
})
