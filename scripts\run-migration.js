#!/usr/bin/env node

/**
 * Database Migration Runner
 * Executes critical security fixes for the products management system
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  console.log('🚀 Starting critical security fixes migration...')

  try {
    // Step 1: Add tenant_id to packages table
    console.log('📝 Step 1: Adding tenant_id column to packages table...')

    const { error: addColumnError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add tenant_id column to packages table
        ALTER TABLE packages
        ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
      `
    })

    if (addColumnError) {
      console.error('❌ Failed to add tenant_id column:', addColumnError)
      throw addColumnError
    }

    console.log('✅ tenant_id column added successfully')

    // Step 2: Backfill tenant_id for existing packages
    console.log('📝 Step 2: Backfilling tenant_id for existing packages...')

    const { error: backfillError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Backfill tenant_id for existing packages by joining with products
        UPDATE packages
        SET tenant_id = products.tenant_id
        FROM products
        WHERE packages.product_id = products.id
        AND packages.tenant_id IS NULL;
      `
    })

    if (backfillError) {
      console.error('❌ Failed to backfill tenant_id:', backfillError)
      throw backfillError
    }

    console.log('✅ tenant_id backfilled successfully')

    // Step 3: Make tenant_id NOT NULL
    console.log('📝 Step 3: Making tenant_id NOT NULL...')

    const { error: notNullError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Make tenant_id NOT NULL after backfill
        ALTER TABLE packages
        ALTER COLUMN tenant_id SET NOT NULL;
      `
    })

    if (notNullError) {
      console.error('❌ Failed to set tenant_id NOT NULL:', notNullError)
      throw notNullError
    }

    console.log('✅ tenant_id set to NOT NULL successfully')
    
    // Run verification queries
    console.log('🔍 Running verification checks...')
    
    // Check if tenant_id was added to packages
    const { data: packageColumns, error: colError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'packages')
      .eq('column_name', 'tenant_id')
    
    if (colError) {
      console.warn('⚠️ Could not verify tenant_id column:', colError.message)
    } else if (packageColumns && packageColumns.length > 0) {
      console.log('✅ tenant_id column added to packages table')
    } else {
      console.error('❌ tenant_id column not found in packages table')
    }
    
    // Check indexes
    const { data: indexes, error: idxError } = await supabase
      .from('pg_indexes')
      .select('indexname, tablename')
      .in('tablename', ['products', 'packages'])
      .like('indexname', 'idx_%tenant%')
    
    if (idxError) {
      console.warn('⚠️ Could not verify indexes:', idxError.message)
    } else {
      console.log(`✅ Created ${indexes?.length || 0} tenant-aware indexes`)
      indexes?.forEach(idx => {
        console.log(`   - ${idx.indexname} on ${idx.tablename}`)
      })
    }
    
    // Check RLS policies
    const { data: policies, error: polError } = await supabase
      .from('pg_policies')
      .select('policyname, tablename')
      .eq('tablename', 'packages')
    
    if (polError) {
      console.warn('⚠️ Could not verify RLS policies:', polError.message)
    } else {
      console.log(`✅ Updated RLS policies for packages table`)
      policies?.forEach(policy => {
        console.log(`   - ${policy.policyname}`)
      })
    }
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('📋 Next steps:')
    console.log('   1. Update API endpoints to use tenant_id in packages operations')
    console.log('   2. Fix duplicate field assignments in product creation')
    console.log('   3. Implement transaction safety for product+packages operations')
    console.log('   4. Update pricing validation logic')
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️ Migration interrupted by user')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️ Migration terminated')
  process.exit(1)
})

// Run the migration
runMigration().catch(error => {
  console.error('💥 Migration failed:', error)
  process.exit(1)
})
