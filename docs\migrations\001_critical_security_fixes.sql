-- =====================================================
-- CRITICAL SECURITY FIXES FOR PRODUCTS MANAGEMENT SYSTEM
-- Migration: 001_critical_security_fixes.sql
-- Date: 2025-01-23
-- =====================================================

-- This migration addresses critical security vulnerabilities in the products management system
-- MUST BE EXECUTED IMMEDIATELY to ensure tenant isolation

BEGIN;

-- =====================================================
-- 1. ADD TENANT_ID TO PACKAGES TABLE (CRITICAL)
-- =====================================================

-- Add tenant_id column to packages table
ALTER TABLE packages 
ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;

-- Backfill tenant_id for existing packages by joining with products
UPDATE packages 
SET tenant_id = products.tenant_id 
FROM products 
WHERE packages.product_id = products.id;

-- Make tenant_id NOT NULL after backfill
ALTER TABLE packages 
ALTER COLUMN tenant_id SET NOT NULL;

-- =====================================================
-- 2. CREATE COMPOSITE INDEXES FOR PERFORMANCE
-- =====================================================

-- Products table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_slug 
ON products(tenant_id, slug);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_category 
ON products(tenant_id, category);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_featured 
ON products(tenant_id, featured) WHERE featured = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_created 
ON products(tenant_id, created_at DESC);

-- Packages table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_packages_tenant_product 
ON packages(tenant_id, product_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_packages_tenant_codes 
ON packages(tenant_id, has_digital_codes) WHERE has_digital_codes = true;

-- =====================================================
-- 3. UPDATE RLS POLICIES FOR PACKAGES TABLE
-- =====================================================

-- Drop existing permissive policies
DROP POLICY IF EXISTS "Packages are viewable by everyone" ON packages;
DROP POLICY IF EXISTS "Only admins can modify packages" ON packages;

-- Create proper tenant-aware RLS policies
CREATE POLICY "Packages viewable by same tenant" ON packages 
FOR SELECT USING (
  tenant_id = (
    SELECT COALESCE(
      (SELECT tenant_id FROM user_profiles WHERE id = auth.uid()),
      (SELECT id FROM tenants WHERE slug = 'main')
    )
  )
);

CREATE POLICY "Admins can manage packages in their tenant" ON packages 
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles up
    WHERE up.id = auth.uid()
    AND up.role = 'admin'
    AND (up.tenant_id = packages.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);

-- =====================================================
-- 4. ADD FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add constraint to ensure packages belong to products in same tenant
ALTER TABLE packages 
ADD CONSTRAINT fk_packages_tenant_product 
CHECK (
  tenant_id = (SELECT tenant_id FROM products WHERE id = product_id)
);

-- =====================================================
-- 5. CREATE UPDATED_AT TRIGGERS
-- =====================================================

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to products and packages tables
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at 
BEFORE UPDATE ON products
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_packages_updated_at ON packages;
CREATE TRIGGER update_packages_updated_at 
BEFORE UPDATE ON packages
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. ADD ENHANCED PRICING FIELDS TO PACKAGES
-- =====================================================

-- Add new pricing fields if they don't exist
ALTER TABLE packages 
ADD COLUMN IF NOT EXISTS user_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS discount_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS distributor_price DECIMAL(10,2);

-- Backfill user_price from existing price field
UPDATE packages 
SET user_price = price 
WHERE user_price IS NULL AND price IS NOT NULL;

-- Add constraints for pricing logic
ALTER TABLE packages 
ADD CONSTRAINT chk_packages_user_price_positive 
CHECK (user_price >= 0);

ALTER TABLE packages 
ADD CONSTRAINT chk_packages_original_price_positive 
CHECK (original_price >= 0);

ALTER TABLE packages 
ADD CONSTRAINT chk_packages_pricing_logic 
CHECK (user_price > original_price);

-- =====================================================
-- 7. ADD ENHANCED PRICING FIELDS TO PRODUCTS
-- =====================================================

-- Add new pricing fields to products table if they don't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS user_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS discount_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS distributor_price DECIMAL(10,2);

-- Add constraints for product pricing
ALTER TABLE products 
ADD CONSTRAINT chk_products_pricing_positive 
CHECK (
  (original_price IS NULL OR original_price >= 0) AND
  (user_price IS NULL OR user_price >= 0) AND
  (discount_price IS NULL OR discount_price >= 0) AND
  (distributor_price IS NULL OR distributor_price >= 0)
);

-- =====================================================
-- 8. CREATE AUDIT LOG TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE'
  user_id UUID REFERENCES auth.users(id),
  old_data JSONB,
  new_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit log queries
CREATE INDEX IF NOT EXISTS idx_product_audit_tenant_product 
ON product_audit_log(tenant_id, product_id, created_at DESC);

-- Enable RLS on audit log
ALTER TABLE product_audit_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can view audit logs in their tenant" ON product_audit_log 
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles up
    WHERE up.id = auth.uid()
    AND up.role = 'admin'
    AND (up.tenant_id = product_audit_log.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify tenant_id was added to packages
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'packages' AND column_name = 'tenant_id';

-- Verify indexes were created
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename IN ('products', 'packages') 
AND indexname LIKE 'idx_%tenant%';

-- Verify RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'packages';
